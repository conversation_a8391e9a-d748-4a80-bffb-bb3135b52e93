using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Technoloway.Core.Entities;
using Technoloway.Core.Interfaces;
using System.Text.Json;

namespace Technoloway.Web.Controllers.Api
{
    [ApiController]
    [Route("api/chatbot")]
    public class ChatbotApiController : ControllerBase
    {
        private readonly IRepository<ChatbotIntent> _intentRepository;
        private readonly IRepository<ChatbotResponse> _responseRepository;
        private readonly IRepository<ChatbotKeyword> _keywordRepository;
        private readonly IRepository<Project> _projectRepository;
        private readonly IRepository<TeamMember> _teamRepository;
        private readonly IRepository<Testimonial> _testimonialRepository;

        public ChatbotApiController(
            IRepository<ChatbotIntent> intentRepository,
            IRepository<ChatbotResponse> responseRepository,
            IRepository<ChatbotKeyword> keywordRepository,
            IRepository<Project> projectRepository,
            IRepository<TeamMember> teamRepository,
            IRepository<Testimonial> testimonialRepository)
        {
            _intentRepository = intentRepository;
            _responseRepository = responseRepository;
            _keywordRepository = keywordRepository;
            _projectRepository = projectRepository;
            _teamRepository = teamRepository;
            _testimonialRepository = testimonialRepository;
        }

        [HttpPost("message")]
        public async Task<IActionResult> ProcessMessage([FromBody] ChatbotMessageRequest request)
        {
            try
            {
                var intent = await DetectIntent(request.Message);
                if (intent != null)
                {
                    var response = await GetResponseForIntent(intent);
                    return Ok(response);
                }

                // Default response if no intent matched
                return Ok(new ChatbotMessageResponse
                {
                    Content = "I'm not sure I understand. Could you please rephrase your question or choose from the options below?",
                    QuickActions = new List<QuickAction>
                    {
                        new QuickAction { Label = "Our Services", Value = "services", Icon = "fas fa-code" },
                        new QuickAction { Label = "Get Quote", Value = "quote", Icon = "fas fa-calculator" },
                        new QuickAction { Label = "Portfolio", Value = "portfolio", Icon = "fas fa-briefcase" },
                        new QuickAction { Label = "Talk to Human", Value = "human", Icon = "fas fa-user" }
                    }
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { error = "Internal server error" });
            }
        }

        [HttpGet("projects")]
        public async Task<IActionResult> GetProjects()
        {
            try
            {
                var projects = await _projectRepository.GetAll()
                    .Where(p => p.IsActive && p.IsFeatured)
                    .OrderByDescending(p => p.CreatedAt)
                    .Take(10)
                    .Select(p => new
                    {
                        p.Name,
                        p.Description,
                        p.ClientName,
                        Service = p.Category,
                        Technologies = p.Technologies.Split(',').Select(t => t.Trim()).ToList()
                    })
                    .ToListAsync();

                return Ok(projects);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { error = "Internal server error" });
            }
        }

        [HttpGet("team")]
        public async Task<IActionResult> GetTeam()
        {
            try
            {
                var team = await _teamRepository.GetAll()
                    .Where(t => t.IsActive)
                    .OrderBy(t => t.DisplayOrder)
                    .Take(6)
                    .Select(t => new
                    {
                        t.Name,
                        t.Position,
                        Bio = t.Bio.Length > 100 ? t.Bio.Substring(0, 100) + "..." : t.Bio
                    })
                    .ToListAsync();

                return Ok(team);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { error = "Internal server error" });
            }
        }

        [HttpGet("testimonials")]
        public async Task<IActionResult> GetTestimonials()
        {
            try
            {
                var testimonials = await _testimonialRepository.GetAll()
                    .Where(t => t.IsActive)
                    .OrderByDescending(t => t.CreatedAt)
                    .Take(8)
                    .Select(t => new
                    {
                        t.ClientName,
                        t.ClientPosition,
                        t.ClientCompany,
                        t.Content,
                        t.Rating
                    })
                    .ToListAsync();

                return Ok(testimonials);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { error = "Internal server error" });
            }
        }

        [HttpPost("contact")]
        public async Task<IActionResult> SubmitContact([FromBody] ChatbotContactRequest request)
        {
            try
            {
                // Here you would typically save the contact request to database
                // For now, we'll just return success
                // You can implement contact saving logic based on your Contact entity

                return Ok(new { success = true, message = "Contact information received" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { error = "Internal server error" });
            }
        }

        private async Task<ChatbotIntent?> DetectIntent(string message)
        {
            var keywords = await _keywordRepository.GetAll()
                .Include(k => k.ChatbotIntent)
                .Where(k => k.IsActive && k.ChatbotIntent.IsActive)
                .ToListAsync();

            var messageLower = message.ToLower();
            var matchedKeywords = new List<(ChatbotKeyword keyword, int score)>();

            foreach (var keyword in keywords)
            {
                var keywordText = keyword.Keyword.ToLower();
                var synonyms = keyword.Synonyms?.Split(',').Select(s => s.Trim().ToLower()).ToList() ?? new List<string>();

                int score = 0;

                // Exact match
                if (keyword.MatchType == "exact" && messageLower.Contains(keywordText))
                {
                    score = keyword.Weight * 2;
                }
                // Partial match
                else if (keyword.MatchType == "partial" && messageLower.Contains(keywordText))
                {
                    score = keyword.Weight;
                }
                // Synonym match
                else if (synonyms.Any(s => messageLower.Contains(s)))
                {
                    score = keyword.Weight;
                }

                if (score > 0)
                {
                    matchedKeywords.Add((keyword, score));
                }
            }

            if (matchedKeywords.Any())
            {
                // Group by intent and sum scores
                var intentScores = matchedKeywords
                    .GroupBy(mk => mk.keyword.ChatbotIntentId)
                    .Select(g => new { IntentId = g.Key, TotalScore = g.Sum(mk => mk.score) })
                    .OrderByDescending(i => i.TotalScore)
                    .First();

                return await _intentRepository.GetByIdAsync(intentScores.IntentId);
            }

            return null;
        }

        private async Task<ChatbotMessageResponse> GetResponseForIntent(ChatbotIntent intent)
        {
            var responses = await _responseRepository.GetAll()
                .Include(r => r.QuickActions)
                .Where(r => r.ChatbotIntentId == intent.Id && r.IsActive)
                .OrderBy(r => r.DisplayOrder)
                .ToListAsync();

            if (!responses.Any())
            {
                return new ChatbotMessageResponse
                {
                    Content = "I understand your question, but I don't have a specific response ready. Let me connect you with our team.",
                    QuickActions = new List<QuickAction>
                    {
                        new QuickAction { Label = "Talk to Human", Value = "human", Icon = "fas fa-user" },
                        new QuickAction { Label = "Get Quote", Value = "quote", Icon = "fas fa-calculator" }
                    }
                };
            }

            // For now, return the first response. You could implement logic to select based on conditions
            var selectedResponse = responses.First();

            var quickActions = selectedResponse.QuickActions?
                .Where(qa => qa.IsActive)
                .OrderBy(qa => qa.DisplayOrder)
                .Select(qa => new QuickAction
                {
                    Label = qa.Label,
                    Value = qa.ActionValue,
                    Icon = qa.IconClass ?? "fas fa-arrow-right"
                })
                .ToList() ?? new List<QuickAction>();

            return new ChatbotMessageResponse
            {
                Content = selectedResponse.Content,
                QuickActions = quickActions
            };
        }
    }

    // Request/Response models
    public class ChatbotMessageRequest
    {
        public string Message { get; set; } = string.Empty;
        public Dictionary<string, object> Context { get; set; } = new();
    }

    public class ChatbotMessageResponse
    {
        public string Content { get; set; } = string.Empty;
        public List<QuickAction> QuickActions { get; set; } = new();
        public List<string> Suggestions { get; set; } = new();
    }

    public class QuickAction
    {
        public string Label { get; set; } = string.Empty;
        public string Value { get; set; } = string.Empty;
        public string Icon { get; set; } = string.Empty;
    }

    public class ChatbotContactRequest
    {
        public string Name { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string Phone { get; set; } = string.Empty;
        public string Company { get; set; } = string.Empty;
        public string ContactMethod { get; set; } = string.Empty;
        public string ContactTime { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public string ProjectType { get; set; } = string.Empty;
        public string BudgetRange { get; set; } = string.Empty;
        public string Timeline { get; set; } = string.Empty;
    }
}
